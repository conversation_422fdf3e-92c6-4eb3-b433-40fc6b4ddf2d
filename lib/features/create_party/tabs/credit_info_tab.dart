import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/widgets/app_text.dart';
import '../bloc/create_party_bloc.dart';
import '../bloc/create_party_event.dart';
import '../bloc/create_party_state.dart';
import '../widgets/credit_period_bottom_sheet.dart';

class CreditInfoTab extends StatelessWidget {
  const CreditInfoTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Container(
          color: Colors.white,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                _buildOpeningBalanceField(),
                const SizedBox(height: 16),
                _buildCreditPeriodField(),
                const SizedBox(height: 16),
                _buildCreditLimitField(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildOpeningBalanceField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const AppText(
                  'Opening Balance',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF374151),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () {
                    // Show tooltip or help
                  },
                  child: const Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                // Currency symbol and amount field
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    initialValue: state.openingBalance == 0.0
                        ? ''
                        : state.openingBalance.toString(),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      final amount = double.tryParse(value) ?? 0.0;
                      context.read<CreatePartyBloc>().add(
                        OpeningBalanceChanged(amount),
                      );
                    },
                    decoration: InputDecoration(
                      isDense: true,
                      prefixIconConstraints: const BoxConstraints(
                        minWidth: 0,
                        minHeight: 0,
                      ),
                      prefixIcon: const Padding(
                        padding: EdgeInsets.only(
                          left: 16,
                          right: 8,
                          top: 10,
                          bottom: 10,
                        ),
                        child: AppText(
                          '₹',
                          fontSize: 16,
                          fontWeight: FontWeight.normal,
                          color: Color(0xFF374151),
                        ),
                      ),
                      hintText: '0.0',
                      hintStyle: TextStyle(color: Colors.grey[400]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          color: Color(0xFF5A67D8),
                          width: 2,
                        ),
                      ),
                      errorBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Colors.red),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 0,
                      ),
                      errorText: state.fieldErrors['openingBalance'],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // Balance type buttons
                Expanded(
                  flex: 2,
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            HapticFeedback.selectionClick();
                            context.read<CreatePartyBloc>().add(
                              const BalanceTypeChanged('receive'),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                              color: state.balanceType == 'receive'
                                  ? const Color(0xFF5A67D8)
                                  : Colors.transparent,
                              border: Border.all(
                                color: state.balanceType == 'receive'
                                    ? const Color(0xFF5A67D8)
                                    : const Color(0xFFE5E7EB),
                              ),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(8),
                                bottomLeft: Radius.circular(8),
                              ),
                            ),
                            child: Center(
                              child: AppText(
                                'I receive',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: state.balanceType == 'receive'
                                    ? Colors.white
                                    : const Color(0xFF374151),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            HapticFeedback.selectionClick();
                            context.read<CreatePartyBloc>().add(
                              const BalanceTypeChanged('pay'),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            decoration: BoxDecoration(
                              color: state.balanceType == 'pay'
                                  ? const Color(0xFF5A67D8)
                                  : Colors.transparent,
                              border: Border.all(
                                color: state.balanceType == 'pay'
                                    ? const Color(0xFF5A67D8)
                                    : const Color(0xFFE5E7EB),
                              ),
                              borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(8),
                                bottomRight: Radius.circular(8),
                              ),
                            ),
                            child: Center(
                              child: AppText(
                                'I pay',
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: state.balanceType == 'pay'
                                    ? Colors.white
                                    : const Color(0xFF374151),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildCreditPeriodField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const AppText(
                  'Credit Period(Days)',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF374151),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () {
                    // Show tooltip or help
                  },
                  child: const Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () => _showCreditPeriodBottomSheet(context),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE5E7EB)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    AppText(
                      '${state.creditPeriodDays} Days',
                      fontSize: 14,
                      color: const Color(0xFF374151),
                    ),
                    const Icon(
                      Icons.keyboard_arrow_down,
                      color: Color(0xFF9CA3AF),
                    ),
                  ],
                ),
              ),
            ),
            if (state.fieldErrors['creditPeriod'] != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: AppText(
                  state.fieldErrors['creditPeriod']!,
                  fontSize: 12,
                  color: Colors.red,
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildCreditLimitField() {
    return BlocBuilder<CreatePartyBloc, CreatePartyState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const AppText(
                  'Credit Limit',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF374151),
                ),
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: () {
                    // Show tooltip or help
                  },
                  child: const Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            TextFormField(
              initialValue: state.creditLimit == 0.0
                  ? ''
                  : state.creditLimit.toString(),
              keyboardType: const TextInputType.numberWithOptions(
                decimal: true,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
              ],
              onChanged: (value) {
                final amount = double.tryParse(value) ?? 0.0;
                context.read<CreatePartyBloc>().add(CreditLimitChanged(amount));
              },
              decoration: InputDecoration(
                isDense: true,
                prefixIconConstraints: const BoxConstraints(
                  minWidth: 0,
                  minHeight: 0,
                ),
                prefixIcon: const Padding(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 8,
                    top: 10,
                    bottom: 10,
                  ),
                  child: AppText(
                    '₹',
                    fontSize: 16,
                    fontWeight: FontWeight.normal,
                    color: Color(0xFF374151),
                  ),
                ),
                hintText: '500.0',
                hintStyle: TextStyle(color: Colors.grey[400]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(
                    color: Color(0xFF5A67D8),
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Colors.red),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 0,
                ),
                errorText: state.fieldErrors['creditLimit'],
              ),
            ),
          ],
        );
      },
    );
  }

  void _showCreditPeriodBottomSheet(BuildContext context) {
    final bloc = context.read<CreatePartyBloc>();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => CreditPeriodBottomSheet(
        selectedPeriod: bloc.state.creditPeriodDays,
        onPeriodSelected: (period) {
          bloc.add(CreditPeriodChanged(period));
        },
      ),
    );
  }
}
