import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/router/app_routes.dart';

class ItemsTab extends StatefulWidget {
  const ItemsTab({super.key});

  @override
  State<ItemsTab> createState() => _ItemsTabState();
}

class _ItemsTabState extends State<ItemsTab> {
  String _selectedFilter = '';
  final String _selectedCategory = 'Select Category';

  // Search state variables
  bool _isSearchMode = false;
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';

  // Filter state variables
  String _sortBy = '';
  final Set<String> _selectedStockFilters = {};

  // Import state variables
  String _selectedBillingSoftware = 'None';

  // Mock data for items
  final List<Map<String, dynamic>> _items = [
    {
      'id': '1',
      'name': 'Sample Item',
      'description': 'Amul Butter 500gm',
      'salesPrice': 220.0,
      'purchasePrice': 190.0,
      'stock': 123.0,
      'unit': 'BOX',
      'avatar': 'S',
      'avatarColor': Colors.grey[300],
    },
    {
      'id': '2',
      'name': 'hi',
      'description': '',
      'salesPrice': 15.0,
      'purchasePrice': 10.0,
      'stock': 158.0,
      'unit': 'PCS',
      'avatar': 'H',
      'avatarColor': Colors.grey[300],
    },
  ];

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Column(
              children: [
                // Header with animation
                AnimatedSwitcher(
                  duration: const Duration(milliseconds: 300),
                  transitionBuilder:
                      (Widget child, Animation<double> animation) {
                        return SlideTransition(
                          position: animation.drive(
                            Tween(
                              begin: const Offset(0.0, -0.5),
                              end: Offset.zero,
                            ).chain(CurveTween(curve: Curves.easeOutCubic)),
                          ),
                          child: FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        );
                      },
                  child: _isSearchMode ? _buildSearchHeader() : _buildHeader(),
                ),

                // Filter Tabs
                _buildFilterTabs(),

                // Items List
                Expanded(child: _buildItemsList()),
              ],
            ),

            // Floating Action Buttons
            _buildFloatingButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 350),
      child: SlideAnimation(
        verticalOffset: -20.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            key: const ValueKey('normal_header'),
            padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
            color: Colors.white,
            child: Row(
              children: [
                const Expanded(
                  child: AppText(
                    'Items',
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        _toggleSearchMode();
                      },
                      icon: const Icon(
                        Icons.search,
                        color: Colors.grey,
                        size: 24,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        Navigator.pushNamed(context, ItemSettingsRoute.name);
                      },
                      icon: const Icon(
                        Icons.settings,
                        color: Colors.grey,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterTabs() {
    return AnimationConfiguration.staggeredList(
      position: 1,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        horizontalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            color: Colors.white,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                spacing: 10,
                children: [
                  _buildFilterChip('Low Stock'),
                  _buildCategoryDropdown(),
                  _buildFilterButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    final bool isSelected = _selectedFilter == label;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          if (isSelected) {
            // Clear the filter if already selected
            _selectedFilter = '';
          } else {
            _selectedFilter = label;
          }
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 12 : 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              label,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : Colors.grey[700],
            ),
            if (isSelected) ...[
              const SizedBox(width: 6),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _selectedFilter = '';
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(2),
                  child: const Icon(Icons.close, size: 12, color: Colors.white),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // Handle category dropdown tap
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              _selectedCategory,
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
            const SizedBox(width: 4),
            Icon(Icons.keyboard_arrow_down, size: 16, color: Colors.grey[700]),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showFilterBottomSheet();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            AppText(
              'Filter By',
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
            const SizedBox(width: 4),
            Icon(Icons.tune, size: 16, color: Colors.grey[700]),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsList() {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _items.length,
        itemBuilder: (context, index) {
          final item = _items[index];
          return AnimationConfiguration.staggeredList(
            position: index + 2,
            duration: const Duration(milliseconds: 300),
            delay: const Duration(milliseconds: 100),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildItemCard(item, index),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildItemCard(Map<String, dynamic> item, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: index < _items.length - 1 ? 16 : 0),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar
          Container(
            width: 42,
            height: 42,
            decoration: BoxDecoration(
              color: item['avatarColor'],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: AppText(
                item['avatar'],
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black54,
              ),
            ),
          ),
          const SizedBox(width: 16),

          // Item Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          item['name'],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        if (item['description'].isNotEmpty) ...[
                          const SizedBox(height: 2),
                          AppText(
                            item['description'],
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ],
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AppText(
                          '${item['stock'].toStringAsFixed(2)}',
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        AppText(
                          item['unit'],
                          fontSize: 10,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const AppText(
                            'Sales Price',
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                          AppText(
                            '₹ ${item['salesPrice'].toStringAsFixed(0)}',
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const AppText(
                            'Purchase Price',
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                          AppText(
                            '₹ ${item['purchasePrice'].toStringAsFixed(0)}',
                            fontSize: 12,
                            color: Colors.black87,
                          ),
                        ],
                      ),
                    ),
                    Builder(
                      builder: (BuildContext buttonContext) {
                        return GestureDetector(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            _showItemOptionsMenu(buttonContext, item, index);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF5A67D8,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.more_horiz,
                              size: 16,
                              color: Color(0xFF5A67D8),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingButtons() {
    return AnimationConfiguration.staggeredList(
      position: 3,
      duration: const Duration(milliseconds: 400),
      delay: const Duration(milliseconds: 200),
      child: SlideAnimation(
        verticalOffset: 50.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Padding(
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
              bottom: 12,
              top: 8,
            ),
            child: Row(
              children: [
                // Create New Item Button
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.pushNamed(context, CreateItemRoute.name);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF5A67D8), Color(0xFF7C4DFF)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(
                              0xFF5A67D8,
                            ).withValues(alpha: 0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.add, color: Colors.white, size: 20),
                          SizedBox(width: 8),
                          AppText(
                            'Create New Item',
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.white,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Import Button
                GestureDetector(
                  onTap: () {
                    HapticFeedback.lightImpact();
                    _showImportBottomSheet();
                  },
                  child: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(28),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.file_download_outlined,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (_isSearchMode) {
        // Focus the search field when entering search mode
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _searchFocusNode.requestFocus();
        });
      } else {
        // Clear search when exiting search mode
        _searchController.clear();
        _searchQuery = '';
        _searchFocusNode.unfocus();
      }
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
  }

  void _clearSearch() {
    setState(() {
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showItemOptionsMenu(
    BuildContext context,
    Map<String, dynamic> item,
    int index,
  ) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset(0, 30), ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset(0, 0)),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    showMenu<String>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      color: Colors.white,
      items: [
        PopupMenuItem<String>(
          value: 'add_stock',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.add_circle_outline,
                  size: 16,
                  color: Color(0xFF5A67D8),
                ),
              ),
              const SizedBox(width: 12),
              const AppText(
                'Add Stock',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'reduce_stock',
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Icon(
                  Icons.remove_circle_outline,
                  size: 16,
                  color: Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              const AppText(
                'Reduce Stock',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        ),
      ],
    ).then((value) {
      if (value == null) return;

      HapticFeedback.lightImpact();

      switch (value) {
        case 'add_stock':
          // Handle add stock action
          _handleAddStock(item);
          break;
        case 'reduce_stock':
          // Handle reduce stock action
          _handleReduceStock(item);
          break;
      }
    });
  }

  void _handleAddStock(Map<String, dynamic> item) {
    Navigator.pushNamed(
      context,
      '/adjust-stock',
      arguments: {...item, 'initialStockType': 'add'},
    );
  }

  void _handleReduceStock(Map<String, dynamic> item) {
    Navigator.pushNamed(
      context,
      '/adjust-stock',
      arguments: {...item, 'initialStockType': 'reduce'},
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      key: const ValueKey('search_header'),
      padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
      color: Colors.white,
      child: Row(
        children: [
          // Back button
          IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _toggleSearchMode();
            },
            icon: const Icon(Icons.arrow_back, color: Colors.grey, size: 24),
          ),
          // Search field
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                focusNode: _searchFocusNode,
                onChanged: _onSearchChanged,
                decoration: InputDecoration(
                  hintText: 'Search items...',
                  hintStyle: TextStyle(color: Colors.grey[600], fontSize: 14),
                  prefixIcon: Icon(
                    Icons.search,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          onPressed: _clearSearch,
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                ),
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFilterBottomSheet(),
    );
  }

  Widget _buildFilterBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.65,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildBottomSheetHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: AnimationLimiter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 300),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 30.0,
                            child: FadeInAnimation(child: widget),
                          ),
                          children: [
                            _buildSortBySection(setModalState),
                            const SizedBox(height: 24),
                            _buildFilterBySection(setModalState),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Action Buttons
                _buildBottomSheetActions(setModalState),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Sort & Filter',
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 20, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortBySection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const AppText(
              'Sort By',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
            const Spacer(),
            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setModalState(() {
                  _sortBy = '';
                });
              },
              child: const AppText(
                'CLEAR',
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFF5A67D8),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildSortOption('Item name - A to Z', 'name_asc', setModalState),
        const SizedBox(height: 12),
        _buildSortOption('Item name - Z to A', 'name_desc', setModalState),
        const SizedBox(height: 12),
        _buildSortOption(
          'Quantity - Low to High',
          'quantity_asc',
          setModalState,
        ),
        const SizedBox(height: 12),
        _buildSortOption(
          'Quantity - High to Low',
          'quantity_desc',
          setModalState,
        ),
      ],
    );
  }

  Widget _buildSortOption(
    String title,
    String value,
    StateSetter setModalState,
  ) {
    final bool isSelected = _sortBy == value;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setModalState(() {
          _sortBy = value;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            _buildSortIcon(value),
            const SizedBox(width: 12),
            Expanded(
              child: AppText(title, fontSize: 14, color: Colors.black87),
            ),
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.grey[400]!,
                  width: 2,
                ),
                color: isSelected
                    ? const Color(0xFF5A67D8)
                    : Colors.transparent,
              ),
              child: isSelected
                  ? const Icon(Icons.check, size: 12, color: Colors.white)
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSortIcon(String value) {
    IconData iconData;
    Color iconColor = const Color(0xFF5A67D8);

    switch (value) {
      case 'name_asc':
        iconData = Icons.sort_by_alpha;
        break;
      case 'name_desc':
        iconData = Icons.sort_by_alpha;
        break;
      case 'quantity_desc':
        iconData = Icons.trending_down;
        break;
      case 'quantity_asc':
        iconData = Icons.trending_up;
        break;
      default:
        iconData = Icons.sort;
    }

    return Icon(iconData, size: 20, color: iconColor);
  }

  Widget _buildFilterBySection(StateSetter setModalState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Filter By',
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildFilterChipOption('Low Stock', setModalState),
            _buildFilterChipOption('In Stock', setModalState),
            _buildFilterChipOption('Not in Stock', setModalState),
            _buildFilterChipOption('In Online store', setModalState),
          ],
        ),
      ],
    );
  }

  Widget _buildFilterChipOption(String label, StateSetter setModalState) {
    final bool isSelected = _selectedStockFilters.contains(label);

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setModalState(() {
          if (isSelected) {
            _selectedStockFilters.remove(label);
          } else {
            _selectedStockFilters.add(label);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[300]!,
          ),
        ),
        child: AppText(
          label,
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: isSelected ? const Color(0xFF5A67D8) : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildBottomSheetActions(StateSetter setModalState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              // Apply filters and close bottom sheet
              setState(() {
                // Apply the filters to the main state
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5A67D8),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const AppText(
              'Apply',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  void _showImportBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildImportBottomSheet(),
    );
  }

  Widget _buildImportBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                // Header
                _buildImportBottomSheetHeader(),

                // Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: AnimationLimiter(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: AnimationConfiguration.toStaggeredList(
                          duration: const Duration(milliseconds: 300),
                          childAnimationBuilder: (widget) => SlideAnimation(
                            verticalOffset: 30.0,
                            child: FadeInAnimation(child: widget),
                          ),
                          children: [
                            _buildBillingSoftwareSection(setModalState),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                // Action Buttons
                _buildImportBottomSheetActions(setModalState),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildImportBottomSheetHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          const Expanded(
            child: AppText(
              'Which billing software have you used before?',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(Icons.close, size: 20, color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillingSoftwareSection(StateSetter setModalState) {
    final List<String> softwareOptions = [
      'None',
      'Bill Book (pen and paper)',
      'Tally',
      'Busy/Marg',
      'Vyapar',
      'Quickbooks/Zoho Books',
      'Custom Software',
      'Other',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: softwareOptions.map((option) {
        return _buildSoftwareOption(option, setModalState);
      }).toList(),
    );
  }

  Widget _buildSoftwareOption(String option, StateSetter setModalState) {
    final bool isSelected = _selectedBillingSoftware == option;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setModalState(() {
          _selectedBillingSoftware = option;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
        ),
        child: Row(
          children: [
            Expanded(
              child: AppText(option, fontSize: 14, color: Colors.black87),
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.grey[400]!,
                  width: 2,
                ),
                color: Colors.white,
              ),
              child: isSelected
                  ? Container(
                      margin: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xFF5A67D8),
                      ),
                    )
                  : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImportBottomSheetActions(StateSetter setModalState) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              // Handle save action and close bottom sheet
              setState(() {
                // Save the selected billing software preference
              });
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF5A67D8),
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const AppText(
              'Save',
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
