import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/settings_bloc.dart';
import '../bloc/settings_event.dart';
import '../bloc/settings_state.dart';
import '../models/settings_item.dart';

class ItemSettingsScreen extends StatelessWidget {
  const ItemSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => SettingsBloc()..add(const LoadSettings()),
      child: const ItemSettingsView(),
    );
  }
}

class ItemSettingsView extends StatelessWidget {
  const ItemSettingsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context),

            // Content
            Expanded(
              child: BlocBuilder<SettingsBloc, SettingsState>(
                builder: (context, state) {
                  if (state is SettingsLoading) {
                    return const Center(
                      child: CircularProgressIndicator(
                        color: Color(0xFF5A67D8),
                      ),
                    );
                  } else if (state is SettingsLoaded) {
                    return _buildSettingsContent(context, state);
                  } else if (state is SettingsError) {
                    return Center(
                      child: AppText(
                        state.message,
                        fontSize: 16,
                        color: Colors.red,
                        textAlign: TextAlign.center,
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  style: const ButtonStyle(
                    padding: WidgetStatePropertyAll(EdgeInsets.zero),
                    minimumSize: WidgetStatePropertyAll(Size.zero),
                    visualDensity: VisualDensity.compact,
                  ),
                  icon: Icon(
                    Platform.isIOS
                        ? Icons.arrow_back_ios_new_rounded
                        : Icons.arrow_back,
                    color: Colors.black,
                  ),
                ),
                const Expanded(
                  child: AppText(
                    'Item Settings',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // Balance the back button
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsContent(BuildContext context, SettingsLoaded state) {
    return AnimationLimiter(
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: state.sections.length + 1, // +1 for footer
        itemBuilder: (context, index) {
          if (index == state.sections.length) {
            // Footer
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 300),
              child: SlideAnimation(
                verticalOffset: 30.0,
                curve: Curves.easeOutCubic,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: _buildFooter(),
                ),
              ),
            );
          }

          final section = state.sections[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildSection(context, section, state),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSection(
    BuildContext context,
    SettingsSection section,
    SettingsLoaded state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (section.title.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 12, top: 24),
            child: AppText(
              section.title,
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black54,
            ),
          ),
        ],
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: section.items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isLast = index == section.items.length - 1;

              return _buildSettingsItem(context, item, state, !isLast);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    BuildContext context,
    SettingsItem item,
    SettingsLoaded state,
    bool showDivider,
  ) {
    return Column(
      children: [
        InkWell(
          onTap: item.onTap != null
              ? () {
                  HapticFeedback.lightImpact();
                  item.onTap!();
                }
              : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (item.icon != null) ...[
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color:
                          item.iconColor?.withValues(alpha: 0.1) ??
                          Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      item.icon,
                      size: 18,
                      color: item.iconColor ?? Colors.grey,
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppText(
                        item.title,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      if (item.subtitle != null &&
                          item.subtitle!.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        AppText(
                          item.subtitle!,
                          fontSize: 12,
                          color: Colors.grey[600],
                          maxLines: 2,
                        ),
                      ],
                      if (item.type == SettingsItemType.selection) ...[
                        const SizedBox(height: 12),
                        _buildSelectionButtons(context, item, state),
                      ],
                    ],
                  ),
                ),
                if (item.type == SettingsItemType.toggle) ...[
                  const SizedBox(width: 16),
                  _buildToggleSwitch(context, item),
                ] else if (item.hasArrow == true) ...[
                  const SizedBox(width: 16),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Color(0xFF5A67D8),
                  ),
                ],
              ],
            ),
          ),
        ),
        if (showDivider)
          Divider(
            height: 1,
            thickness: 1,
            color: Colors.grey[200],
            indent: item.icon != null ? 64 : 16,
            endIndent: 16,
          ),
      ],
    );
  }

  Widget _buildToggleSwitch(BuildContext context, SettingsItem item) {
    const primaryColor = Color(0xFF5A67D8);
    final isEnabled = item.isEnabled ?? false;

    return Theme(
      data: Theme.of(context).copyWith(
        switchTheme: SwitchThemeData(
          thumbColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey[400]!;
            }
            if (states.contains(WidgetState.selected)) {
              return primaryColor;
            }
            return Colors.white;
          }),
          trackColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey[300]!;
            }
            if (states.contains(WidgetState.selected)) {
              return primaryColor.withValues(alpha: 0.3);
            }
            return Colors.grey[300]!;
          }),
          trackOutlineColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.selected)) {
              return primaryColor.withValues(alpha: 0.5);
            }
            return Colors.grey[400]!;
          }),
          overlayColor: WidgetStateProperty.resolveWith<Color>((states) {
            if (states.contains(WidgetState.pressed)) {
              return primaryColor.withValues(alpha: 0.1);
            }
            return Colors.transparent;
          }),
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          splashRadius: 20,
        ),
      ),
      child: Switch.adaptive(
        value: isEnabled,
        onChanged: item.onToggle != null
            ? (value) {
                HapticFeedback.lightImpact();
                item.onToggle!(value);
              }
            : null,
      ),
    );
  }

  Widget _buildSelectionButtons(
    BuildContext context,
    SettingsItem item,
    SettingsLoaded state,
  ) {
    return Row(
      children:
          item.options?.map((option) {
            final isSelected = option == item.selectedOption;
            return Padding(
              padding: const EdgeInsets.only(right: 12),
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  if (item.onTap != null) {
                    item.onTap!();
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF5A67D8)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF5A67D8)
                          : Colors.grey[300]!,
                    ),
                  ),
                  child: AppText(
                    option,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected ? Colors.white : Colors.grey[700],
                  ),
                ),
              ),
            );
          }).toList() ??
          [],
    );
  }

  Widget _buildFooter() {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.info, size: 12, color: Colors.white),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: AppText(
              'These settings will apply to all items',
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
